 /* Hide footer toggle on large screens */
        @media (min-width: 992px) {
            .footer-toggler {
                display: none !important;
            }
        }
        .footer-copyright p { color: #B1B1B1; font-family: Poppins; font-size: 18px; font-weight: 400; line-height: normal; text-transform: capitalize; }
        .social-content { display: flex; gap: 10px; }
        .social-btn { width: 36px; height: 36px; border-radius: 50%; display: flex; align-items: center; justify-content: center; text-decoration: none; color: white; border: 1px solid #F40009;  font-size: 20px; transition: all 0.3s ease; }
        .service-icon {
            width: 70px;
            height: 70px;
            border-radius: var(--radius-full);
            background: var(--dark-gray);
            border: 2px solid var(--light-gray);
            box-shadow: var(--shadow-lg);
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            transition: all 0.3s ease;
        }

        .service-icon:hover {
            background: var(--medium-gray);
            border-color: var(--primary);
            box-shadow: 0px 0px 35px 0px rgba(244, 0, 9, 0.40);
            transform: scale(1.05);
        }

        .service-icon img {
            width: 35px;
            height: 35px;
            filter: brightness(0) saturate(100%) invert(13%) sepia(94%) saturate(7151%) hue-rotate(356deg) brightness(91%) contrast(118%);
            object-fit: contain;
            transition: all 0.3s ease;
        }

        /*---responsiveness--*/
        @media (max-width: 1200px) {
            .container { padding: 0 20px; }
            .email-form { width: 100%; max-width: 500px; }
            .service-item { margin-bottom: 15px; }

            .image-container { margin-bottom: 30px; }
        }

        @media (max-width: 1180px){
            .hero h1 {font-size: 50px;}
            .hero h1 span {font-size: 50px;}
            .about-heading {font-size: 40px;}
            .about-heading span {font-size: 40px;}
            .communicate-heading h3 {font-size: 40px; width: 100%;}
            .communicate-heading h3 span{font-size: 40px;}
            .testimonials-heading h2 {font-size: 40px;}
            .testimonials-heading h2 span{font-size: 40px;}
            .connacted-heading h3 {font-size: 40px;}
            .connacted-heading h3 span {font-size: 40px;}
        }

        @media (max-width: 992px) {
            .para-text{width: 100%}
            .hero h1{width: 100% !important;}
            .hero { padding: 120px 0 80px; }
            .about { padding-block: 80px; }
            .mission-sec { padding-block: 80px; }
            .mission-heading { width: 100% !important; }
            .testimonials-heading p { width: 100% !important; }
            .communicate-heading p { width: 100% !important; }
            .footer-content { flex-direction: column; gap: 20px; text-align: center; }
            .footer-bottom { flex-direction: column; gap: 20px; text-align: center; }
            .footer-copyright { flex-direction: column; gap: 15px; text-align: center; }
            .footer-nav-container { width: 100%; }
            .footer-nav-list { flex-direction: column; gap: 10px !important; margin-top: 15px; }
            .footer-nav-list li { width: 100%; text-align: center; }
            .service-header { padding: 20px; }
            .service-description { padding: 20px; }
            .testimonial-card { padding: 20px; }
            .contact-card { padding: 25px; }
            .image-text-overlay { bottom: 20px; left: 20px; }
            .overlay-name { font-size: 28px; }
            .overlay-title { font-size: 16px; }
        }

        @media (max-width: 768px) {
            .hero h1 { font-size: 36px; }
            .hero h1 span { font-size: 36px; }
            .about-heading { font-size: 32px; }
            .about-heading span { font-size: 32px; }
            .communicate-heading h3 { font-size: 32px; }
            .communicate-heading h3 span { font-size: 32px; }
            .testimonials-heading h2 { font-size: 32px; }
            .testimonials-heading h2 span { font-size: 32px; }
            .connacted-heading h3 { font-size: 32px; }
            .connacted-heading h3 span { font-size: 32px; }
            .para-text { font-size: 16px; }
            .about-para { font-size: 16px; }
            .service-para { font-size: 16px; }
            .list-content { font-size: 24px; }
            .contact-form textarea { width: 100%; }
            .email-form { flex-direction: column; padding: 16px; }
            .email-input { margin-bottom: 12px; }
            .nav-item { margin-bottom: 8px; }

            .footer-nav-list { gap: 8px !important; }
            .image-container { margin-top: 30px; }
            .service-item { margin-bottom: 12px; }
            .service-header { padding: 18px; }
            .service-title { font-size: 18px; }
            .service-number { font-size: 16px; }
            .service-description { padding: 18px; font-size: 15px; }
            .testimonial-card { padding: 18px; }
            .contact-card { padding: 20px; }
            .contact-items { padding: 8px 0; }
            .overlay-name { font-size: 24px; }
            .overlay-title { font-size: 14px; }
            .image-text-overlay { bottom: 15px; left: 15px; }
        }

        @media (max-width: 576px) {
            .hero { padding: 100px 0 60px; }
            .hero h1 { font-size: 28px; }
            .hero h1 span { font-size: 28px; }
            .about-heading { font-size: 28px; }
            .about-heading span { font-size: 28px; }
            .communicate-heading h3 { font-size: 28px; }
            .communicate-heading h3 span { font-size: 28px; }
            .testimonials-heading h2 { font-size: 28px; }
            .testimonials-heading h2 span { font-size: 28px; }
            .connacted-heading h3 { font-size: 28px; }
            .connacted-heading h3 span { font-size: 28px; }
            .started_btn, .influencer_btn { padding: 10px 24px; font-size: 14px; }
            .service-heading { font-size: 20px; }
            .card-custom h5 { font-size: 22px; }
            .list-content { font-size: 20px; }
            .contact-heading h3 { font-size: 20px; }
            .contact-heading h3 span { font-size: 20px; }
            .footer-logo h2 { font-size: 28px; }
            .about { padding-block: 60px; }
            .mission-sec { padding-block: 60px; }
            .container { padding: 0 15px; }
            .service-item { margin-bottom: 10px; }
            .service-header { padding: 15px; }
            .service-title { font-size: 16px; }
            .service-number { font-size: 14px; }
            .service-description { padding: 15px; font-size: 14px; }
            .testimonial-card { padding: 15px; }
            .contact-card { padding: 18px; }
            .overlay-name { font-size: 20px; }
            .overlay-title { font-size: 12px; }
            .image-text-overlay { bottom: 12px; left: 12px; }
            .communicate-heading p { font-size: 16px; }
            .testimonials-heading p { font-size: 16px; }
        }

        @media (max-width: 480px) {
            .hero h1 { font-size: 24px; line-height: 1.3; }
            .hero h1 span { font-size: 24px; }
            .para-text { font-size: 14px; }
            .started_btn, .influencer_btn { width: 100%; margin-bottom: 10px; }
            .d-flex.justify-content-center.gap-3 { flex-direction: column; align-items: center; }
            .service-heading { font-size: 18px; }
            .service-para { font-size: 14px; }
            .about-para { font-size: 14px; }
            .card-custom p { font-size: 16px; }
            .list-content { font-size: 18px; }
            .testimonial-text { font-size: 14px; }
            .author h2 { font-size: 16px; }
            .contact-text p { font-size: 14px; }
            .footer-copyright p { font-size: 14px; }
            .image-container { margin-top: 20px; }
            .service-item { margin-bottom: 8px; }
            .service-header { padding: 12px; }
            .service-title { font-size: 14px; }
            .service-number { font-size: 12px; }
            .service-description { padding: 12px; font-size: 13px; }
            .testimonial-card { padding: 12px; }
            .contact-card { padding: 15px; }
            .contact-items { padding: 6px 0; }
            .overlay-name { font-size: 18px; }
            .overlay-title { font-size: 11px; }
            .image-text-overlay { bottom: 10px; left: 10px; }
            .communicate-heading h3 { font-size: 24px; }
            .communicate-heading h3 span { font-size: 24px; }
            .testimonials-heading h2 { font-size: 24px; }
            .testimonials-heading h2 span { font-size: 24px; }
            .communicate-heading p { font-size: 14px; }
            .testimonials-heading p { font-size: 14px; }
        }

        @media (max-width: 320px) {
            .hero { padding: 80px 0 40px; }
            .hero h1 { font-size: 20px; line-height: 1.2; }
            .hero h1 span { font-size: 20px; }
            .para-text { font-size: 12px; }
            .started_btn, .influencer_btn { padding: 8px 16px; font-size: 12px; width: 100%; margin-bottom: 8px; }
            .about { padding-block: 40px; }
            .mission-sec { padding-block: 40px; }
            .container { padding: 0 10px; }
            .service-item { margin-bottom: 6px; }
            .service-header { padding: 10px; }
            .service-title { font-size: 12px; }
            .service-number { font-size: 10px; }
            .service-description { padding: 10px; font-size: 12px; }
            .testimonial-card { padding: 10px; }
            .contact-card { padding: 12px; }
            .contact-items { padding: 4px 0; }
            .overlay-name { font-size: 16px; }
            .overlay-title { font-size: 10px; }
            .image-text-overlay { bottom: 8px; left: 8px; }
            .communicate-heading h3 { font-size: 20px; }
            .communicate-heading h3 span { font-size: 20px; }
            .testimonials-heading h2 { font-size: 20px; }
            .testimonials-heading h2 span { font-size: 20px; }
            .about-heading { font-size: 20px; }
            .about-heading span { font-size: 20px; }
            .communicate-heading p { font-size: 12px; }
            .testimonials-heading p { font-size: 12px; }
            .about-para { font-size: 12px; }
            .service-heading { font-size: 16px; }
            .service-para { font-size: 12px; }
            .card-custom h5 { font-size: 16px; }
            .card-custom p { font-size: 12px; }
            .testimonial-text { font-size: 11px; }
            .author h2 { font-size: 14px; }
            .contact-text p { font-size: 11px; }
            .footer-logo h2 { font-size: 18px; }
            .social-btn { width: 28px; height: 28px; font-size: 14px; }
            .email-form { padding: 8px; }
            .email-input { font-size: 12px; }
            .email-btn { font-size: 12px; padding: 6px 12px; }
        }

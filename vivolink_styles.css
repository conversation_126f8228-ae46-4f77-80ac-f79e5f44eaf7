/* Global CSS Variables */
:root {
    /* Colors */
    --primary: #F40009;
    --primary-hover: #d4000a;
    --white: #FFFFFF;
    --black: #000000;
    --dark-bg: #040404;
    --dark-gray: #2a2a2a;
    --medium-gray: #333;
    --light-gray: #444;
    --border-gray: #D4D4D4;
    --light-grey: #191919;
    --para-text: #FFFFFFCC;
    --text-muted: #B1B1B1;
    --text-light: #D9D9D9;
    --text-bright: #F8F8F8;
    --text-very-light: #FAFAFA;
    
    /* Font Sizes */
    --font-xs: 11px;
    --font-sm: 12px;
    --font-base: 14px;
    --font-md: 16px;
    --font-lg: 18px;
    --font-xl: 20px;
    --font-2xl: 24px;
    --font-3xl: 28px;
    --font-4xl: 32px;
    --font-5xl: 36px;
    --font-6xl: 42px;
    --font-7xl: 50px;
    --font-8xl: 54px;
    --font-9xl: 64px;
    --font-10xl: 68px;
    
    /* Font Families */
    --font-primary: 'Satoshi', sans-serif;
    --font-secondary: 'Instrument Serif', serif;
    --font-tertiary: 'Martel Sans', sans-serif;
    --font-quaternary: 'Inter', sans-serif;
    --font-quinary: 'Poppins', sans-serif;
    
    /* Border Radius */
    --radius-sm: 5px;
    --radius-md: 10px;
    --radius-lg: 12px;
    --radius-xl: 20px;
    --radius-full: 50%;
    
    /* Shadows */
    --shadow-sm: 0px 1px 0px 0px rgba(255, 255, 255, 0.08) inset;
    --shadow-md: 0px 0px 15px 0px rgba(0, 0, 0, 0.05);
    --shadow-lg: 0px 0px 29px 0px rgba(244, 0, 9, 0.20);
    --shadow-xl: 0px -4px 4px 0px rgba(233, 223, 255, 0.14);
}

/* Base */
body {
    background: var(--dark-bg);
    color: var(--white);
    font-family: var(--font-primary);
    font-size: var(--font-md);
    line-height: 1.6;
}
.container { max-width: 1366px; width: 100%; }

/* Header */
.navbar-brand img { width: 100%; height: 100%; max-height: 40px; }
header nav { background-color: black; }
.nav-item { padding: 0px 10px; background: var(--light-grey); border-radius: 5px; }
.navbar-nav .nav-link:hover { color: var(--primary); }
.nav-item a { color: var(--white); font-family: 'Satoshi', sans-serif; font-size: 16px; font-weight: 500; line-height: 26px; }
.navbar-toggler { border: 1px solid var(--primary); }
.navbar-toggler:focus { box-shadow: 0 0 0 0.25rem rgba(244, 0, 9, 0.25); }

/* Hero */
.hero::before { content: ''; position: absolute; width: 100%; height: 100%; background: url("{{asset('website/assets/landing_images/Vector.png')}}"); background-size: cover; background-repeat: no-repeat; background-position: center; }
.hero { padding: 150px 0 100px; position: relative; overflow: hidden; }
.highlight { font-family: var(--font-secondary); font-size: var(--font-10xl); font-style: italic; font-weight: 400; line-height: 140%; }
.para-text { color: var(--para-text); text-align: center; font-size: var(--font-lg); width: 66%; font-weight: 500; line-height: 140%; margin: auto; }
.hero h1 { font-size: var(--font-9xl); font-weight: 500; line-height: 140%; font-family: var(--font-primary); }
.started_btn { display: flex; padding: 12px 48px; justify-content: center; align-items: center; border-radius: var(--radius-md); background: var(--primary); color: var(--white); backdrop-filter: blur(10px); text-decoration: none; }
.service-icon img { width: 40%; height: 40%; }
.influencer_btn { display: flex; padding: 12px 48px; justify-content: center; border-radius: var(--radius-md); text-decoration: none; color: var(--white); background: var(--light-grey); backdrop-filter: blur(10px); align-items: center; border: 1px solid #470001; }

/* Cards */
.card { border-radius: var(--radius-xl); border: 1px solid rgba(255, 255, 255, 0.15); background: var(--dark-bg); transition: all 0.3s ease; }
.card:hover { transform: translateY(-10px); box-shadow: 0 20px 50px rgba(255, 0, 64, 0.2); }
.service-heading { color: var(--white); font-family: var(--font-primary); font-size: var(--font-2xl); font-weight: 400; line-height: 35px; }
.service-para { color: var(--para-text); font-family: var(--font-primary); font-size: var(--font-lg); font-weight: 400; line-height: 140%; }
.card-custom img, svg {
    width: 100%;
    object-fit: cover;
    border-radius: 20px;
    display: block;
}

/* About */
.about { padding-block: 100px; }
.about-heading { color: var(--white); font-family: var(--font-primary); font-size: var(--font-8xl); font-weight: 500; line-height: 140%; text-transform: capitalize; }
.about-heading span { color: var(--white); font-family: var(--font-secondary); font-size: var(--font-8xl); font-style: italic; font-weight: 400; line-height: 140%; text-transform: capitalize; }
.about-para { color: var(--para-text); font-family: var(--font-primary); font-size: var(--font-lg); font-weight: 500; line-height: 140%; }
.image-container {
    position: relative;
    width: 100%;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.image-container:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(244, 0, 9, 0.2);
}
.about-image {
    width: 100%;
    height: 400px;
    object-fit: cover;
    display: block;
    border-radius: 20px;
    transition: transform 0.3s ease;
}
.image-container:hover .about-image {
    transform: scale(1.02);
}

/* Text Overlay Styles */
.image-text-overlay {
    position: absolute;
    bottom: 30px;
    left: 30px;
    color: white;
    z-index: 10;
}
.overlay-name {
    font-family: 'Satoshi', sans-serif;
    font-size: 32px;
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 5px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}
.overlay-title {
    font-family: 'Satoshi', sans-serif;
    font-size: 18px;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Services Expandable Styles */
.service-item {
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    margin-bottom: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
    background: white;
}

.service-item:hover {
    border-color: #f40009;
    box-shadow: 0 4px 15px rgba(244, 0, 9, 0.1);
}

.service-header {
    padding: 24px;
    cursor: pointer;
    display: flex;
    justify-content: between;
    align-items: center;
    transition: all 0.3s ease;
    background: white;
}

.service-header:hover {
    background: #f8f9fa;
}

.service-header.active {
    background: #f40009;
    color: white;
}

.service-header.active .service-number,
.service-header.active .service-title {
    color: white;
}

.service-number {
    font-size: 18px;
    font-weight: 600;
    color: #f40009;
    margin-right: 12px;
    transition: color 0.3s ease;
}

.service-title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    flex-grow: 1;
    transition: color 0.3s ease;
}

.service-toggle {
    font-size: 18px;
    color: #666;
    transition: all 0.3s ease;
}

.service-header.active .service-toggle {
    color: white;
    transform: rotate(180deg);
}

.service-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: #f8f9fa;
}

.service-content.active {
    max-height: 200px;
}

.service-description {
    padding: 24px;
    color: #666;
    font-size: 16px;
    line-height: 1.6;
    border-top: 1px solid #e0e0e0;
}

/* Testimonial Cards Fixed Height */
.swiper-slide {
    height: auto;
}

.testimonial-card {
    height: 280px !important;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 24px;
    box-sizing: border-box;
}

.testimonial-text {
    flex-grow: 1;
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.author {
    margin-top: auto;
}

/* Simple scroll animation */
.scroll-animate {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.scroll-animate.show {
    opacity: 1;
    transform: translateY(0);
}

/* 3 Essential Animations */
@keyframes fadeInUp {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInLeft {
    from { opacity: 0; transform: translateX(-30px); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes zoomIn {
    from { opacity: 0; transform: scale(0.8); }
    to { opacity: 1; transform: scale(1); }
}

/* Animation classes */
.fade-up { animation: fadeInUp 0.6s ease forwards; }
.slide-left { animation: slideInLeft 0.6s ease forwards; }
.zoom { animation: zoomIn 0.6s ease forwards; }

/* Hover effects */
.service-item:hover { transform: translateY(-3px); transition: 0.3s; }
.red-btn:hover { transform: translateY(-3px); box-shadow: 0 5px 15px rgba(244,0,9,0.3); transition: 0.3s; }
.testimonial-card:hover { transform: translateY(-3px); transition: 0.3s; }

/* Mission */
.mission-heading { width: 50%; }
.mission-sec { padding-block: 100px; }
.card-custom { border-radius: 20px; border: 1px solid rgba(134, 146, 166, 0.20); background: rgba(255, 255, 255, 0.05); box-shadow: 0px -4px 4px 0px rgba(233, 223, 255, 0.14); padding: 24px; }
.card-custom h5 { color: #FAFAFA; font-family: 'Satoshi', sans-serif; font-size: 28px; font-weight: 500; line-height: 160%; }
.card-custom p { color: rgba(255, 255, 255, 0.80); font-family: 'Satoshi', sans-serif; font-size: 18px; font-weight: 500; line-height: 140%; }

/* Communication */
.communicate-heading h3 { color: #FFF; text-align: center; font-family: 'Satoshi', sans-serif; font-size: 54px; font-weight: 500; line-height: 140%; text-transform: capitalize; width: 60%; margin: auto; }
.communicate-heading span { color: var(--white); font-family: "Instrument Serif"; font-size: 54px; font-style: italic; font-weight: 400; line-height: 140%; text-transform: capitalize; }
.communicate-heading p { color: var(--para-text); text-align: center; font-family: 'Satoshi', sans-serif; font-size: 18px; font-weight: 500; line-height: 140%; }
.list-group-item { border: none; display: flex; justify-content: space-between; align-items: center; padding: 12px 20px; border-bottom: 1px solid #FFFFFF4D; border-radius: 8px; background: none; }
.list-content { color: #FFF; font-family: 'Satoshi', sans-serif; font-size: 34px; font-weight: 500; line-height: 140%; text-transform: capitalize; }

.list-group { padding: 0; }
.chat-anchor { width: 100%; }
i.fa-arrow-right.redirect-arrow{transform: rotate(322deg);}

.chat-call-main a, .chat-call-main span, .chat-call-main i { color: white; }
.redirect-arrow { border-radius: 50%; border: 1px solid #fff; width: 40px; height: 40px; display: flex; justify-content: center; align-items: center; }

/* Testimonials */
.testimonials-heading h2 { color: var(--white); text-align: center; font-family: 'Satoshi', sans-serif; font-size: 54px; font-weight: 500; line-height: 140%; text-transform: capitalize; }
.testimonials-heading span { color: var(--white); font-family: "Instrument Serif"; font-size: 54px; font-style: italic; font-weight: 400; line-height: 140%; text-transform: capitalize; }
.testimonials-heading p { color: var(--para-text); text-align: center; font-family: 'Satoshi', sans-serif; font-size: 18px; font-weight: 500; line-height: 140%; }
.testimonial-card { padding: 24px; display: flex; flex-direction: column; border-radius: 20px; border: 1px solid rgba(134, 146, 166, 0.20); background: rgba(255, 255, 255, 0.05); box-shadow: 0px 1px 0px 0px rgba(255, 255, 255, 0.08) inset; }
.stars { color: #ff4444; width: 24px; margin-bottom: 16px; display: flex; gap: 4px; }
.testimonial-text { color: #D9D9D9; font-family: "Martel Sans"; font-size: 16px; font-weight: 400; line-height: 160%; text-transform: capitalize; }
.author { display: flex; align-items: center; gap: 12px; margin-top: 16px; }
.author h2 { color: #F8F8F8; font-family: "Martel Sans"; font-size: 20px; font-weight: 600; line-height: normal; }
.author-avatar { width: 40px; height: 40px; border-radius: 50%; background: linear-gradient(135deg, #667eea, #764ba2); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 14px; }
.swiper-pagination { position: relative; margin-top: 24px; }
.swiper-pagination-bullet { background: #D9D9D9; width: 12px; height: 12px; border-radius: 50%; border: none; opacity: 1; margin: 0 10px; }
.swiper-pagination-bullet-active { background: transparent; border: 2px solid #F40009; width: 12px; height: 12px; }

/* Contact */
.contact-heading h3 { color: var(--white); text-align: center; font-family: 'Satoshi', sans-serif; font-size: 24px; font-weight: 500; line-height: 140%; text-transform: capitalize; }
.contact-heading h3 span { color: var(--white); font-family: "Instrument Serif"; font-size: 24px; font-style: italic; font-weight: 400; line-height: 140%; text-transform: capitalize; }
.contact-card {border: 1px solid #D4D4D4;background: linear-gradient(180deg, #333 0%, #1D1D1D 100%);box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.05); padding: 30px; border-radius: 10px;  }
.contact-heading p { color: var(--white); text-align: center; font-family: "Martel Sans"; font-size: 16px; font-weight: 400; line-height: 25px; }
.contact-items a { text-decoration: none; color: inherit; transition: all 0.3s ease; display: flex; align-items: center; gap: 15px; padding: 10px 0; }
.contact-icons { width: 45px; height: 45px; display: flex; align-items: center; justify-content: center; flex-shrink: 0; background-color: #111111; border-radius: 50%; }
.contact-icons i { color: white; font-size: 18px; }
.contact-text p { color: #FFF; font-family: "Martel Sans", sans-serif; font-size: 16px; font-weight: 400; line-height: normal; margin: 0; }
.contact-text p:first-child { font-weight: 600; margin-bottom: 2px; }
.social-section { margin-top: 50px; display: flex; align-items: baseline; gap: 10px;}
.social-title { font-size: 24px; font-weight: 400; margin-bottom: 20px; }
.social-links { display: flex; gap: 15px; }
.social-link {border: 1px solid #F40009; width: 36px; height: 36px;  border-radius: 50%; display: flex; align-items: center; justify-content: center; text-decoration: none; color: white; font-size: 20px; transition: all 0.3s ease; }
.social-link:hover { background-color: rgba(0, 0, 0, 0.5); transform: translateY(-2px); }
.connacted-heading h3 { color: var(--white); text-align: center; font-family: 'Satoshi', sans-serif; font-size: 54px; font-weight: 500; line-height: 140%; text-transform: capitalize; }
.connacted-heading h3 span { color: var(--white); font-family: "Instrument Serif"; font-size: 54px; font-style: italic; font-weight: 400; line-height: 140%; text-transform: capitalize; }
.contact-form input { display: flex; height: 48px; padding: 20px; align-items: center; flex: 1 0 0; border-radius: 6px; border: 1px solid #E5E7EB; background: #FFF; }
.contact-form textarea { border-radius: 6px; border: 1px solid #E5E7EB; background: #FFF; display: flex; width: 100%; max-width: 810px; height: 200px; padding: 20px; align-items: flex-start; resize: vertical; }
.red-btn { border:0; display: flex; width: 180px; padding: 12px 0px; justify-content: center; align-items: center; border-radius: 10px; background:#F40009; backdrop-filter: blur(10px); color: white; text-decoration: none; }
.bg-contact { border-radius: 20px; border: 1px solid rgba(134, 146, 166, 0.20); background: rgba(255, 255, 255, 0.05); box-shadow: 0px 1px 0px 0px rgba(255, 255, 255, 0.08) inset; }

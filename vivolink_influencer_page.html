<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Influence - Professional Influence Marketing Platform</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">


    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Swiper/11.0.5/swiper-bundle.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
            href="https://fonts.googleapis.com/css2?family=Instrument+Serif:ital,wght@0,400;0,600;1,400&family=Satoshi:ital,wght@0,300;0,400;0,500;0,600;0,700;0,900;1,300;1,400;1,500;1,600;1,700;1,900&display=swap"
            rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Martel+Sans:wght@200;300;400;600;700;800;900&display=swap"
          rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">


    <style>


        :root {
            --primary: #F40009;
            --dark-bg: #040404;
            --light-grey: #191919;
            --white: #ffff;
            --para-text: #FFFFFFCC;
        }
        /* Base */
        body { background: var(--dark-bg); color: white; font-family: 'Satoshi', sans-serif; }
        .container { max-width: 1366px; width: 100%; }

        /* Header */
        .navbar-brand img { width: 100%; height: 100%; max-height: 40px; }
        header nav { background-color: black; }
        .nav-item { padding: 0px 10px; background: var(--light-grey); border-radius: 5px; }
        .navbar-nav .nav-link:hover { color: var(--primary); }
        .nav-item a { color: var(--white); font-family: 'Satoshi', sans-serif; font-size: 16px; font-weight: 500; line-height: 26px; }
        .navbar-toggler { border: 1px solid var(--primary); }
        .navbar-toggler:focus { box-shadow: 0 0 0 0.25rem rgba(244, 0, 9, 0.25); }

        /* Hero */
        .hero::before { content: ''; position: absolute; width: 100%; height: 100%; background: url("{{asset('website/assets/landing_images/Vector.png')}}"); background-size: cover; background-repeat: no-repeat; background-position: center; }
        .hero { padding: 150px 0 100px; position: relative; overflow: hidden; }
        .highlight { font-family: "Instrument Serif"; font-size: 68px; font-style: italic; font-weight: 400; line-height: 140%; }
        .para-text { color: var(--para-text); text-align: center; font-size: 18px; width: 66%; font-weight: 500; line-height: 140%; margin: auto; }
        .hero h1 { font-size: 64px; font-weight: 500; line-height: 140%; font-family: 'Satoshi', sans-serif; }
        .started_btn { display: flex; padding: 12px 48px; justify-content: center; align-items: center; border-radius: 10px; background: #F40009; color: var(--white); backdrop-filter: blur(10px); text-decoration: none; }
        .service-icon img { width: 40%; height: 40%; }
        .influencer_btn { display: flex; padding: 12px 48px; justify-content: center; border-radius: 10px; text-decoration: none; color: var(--white); background: rgba(255, 255, 255, 0.10); backdrop-filter: blur(10px); align-items: center; border: 1px solid #470001; }

        /* Cards */
        .card { border-radius: 20px; border: 1px solid rgba(255, 255, 255, 0.15); background: #151515; transition: all 0.3s ease; }
        .card:hover { transform: translateY(-10px); box-shadow: 0 20px 50px rgba(255, 0, 64, 0.2); }
        .service-heading { color: var(--white); font-family: 'Satoshi', sans-serif; font-size: 24px; font-weight: 400; line-height: 35px; }
        .service-para { color: var(--para-text); font-family: 'Satoshi', sans-serif; font-size: 18px; font-weight: 400; line-height: 140%; }
        .card-custom img, svg { vertical-align: middle; width: 100%; object-fit: contain; }
        /* About */
        .about { padding-block: 100px; }
        .about-heading { color: var(--white); font-family: 'Satoshi', sans-serif; font-size: 54px; font-weight: 500; line-height: 140%; text-transform: capitalize; }
        .about-heading span { color: var(--white); font-family: "Instrument Serif"; font-size: 54px; font-style: italic; font-weight: 400; line-height: 140%; text-transform: capitalize; }
        .about-para { color: var(--para-text); font-family: 'Satoshi', sans-serif; font-size: 18px; font-weight: 500; line-height: 140%; }
        .image-container {
            position: relative;
            width: 100%;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .image-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(244, 0, 9, 0.2);
        }
        .about-image {
            width: 100%;
            height: auto;
            display: block;
            border-radius: 10px;
            transition: transform 0.3s ease;
        }
        .image-container:hover .about-image {
            transform: scale(1.02);
        }
        /* Mission */
        .mission-heading { width: 50%; }
        .mission-sec { padding-block: 100px; }
        .card-custom { border-radius: 20px; border: 1px solid rgba(134, 146, 166, 0.20); background: rgba(255, 255, 255, 0.05); box-shadow: 0px -4px 4px 0px rgba(233, 223, 255, 0.14); padding: 24px; }
        .card-custom h5 { color: #FAFAFA; font-family: 'Satoshi', sans-serif; font-size: 28px; font-weight: 500; line-height: 160%; }
        .card-custom p { color: rgba(255, 255, 255, 0.80); font-family: 'Satoshi', sans-serif; font-size: 18px; font-weight: 500; line-height: 140%; }

        /* Communication */
        .communicate-heading h3 { color: #FFF; text-align: center; font-family: 'Satoshi', sans-serif; font-size: 54px; font-weight: 500; line-height: 140%; text-transform: capitalize; width: 60%; margin: auto; }
        .communicate-heading span { color: var(--white); font-family: "Instrument Serif"; font-size: 54px; font-style: italic; font-weight: 400; line-height: 140%; text-transform: capitalize; }
        .communicate-heading p { color: var(--para-text); text-align: center; font-family: 'Satoshi', sans-serif; font-size: 18px; font-weight: 500; line-height: 140%; }
        .list-group-item { border: none; display: flex; justify-content: space-between; align-items: center; padding: 12px 20px; border-bottom: 1px solid #FFFFFF4D; border-radius: 8px; background: none; }
        .list-content { color: #FFF; font-family: 'Satoshi', sans-serif; font-size: 34px; font-weight: 500; line-height: 140%; text-transform: capitalize; }

        .list-group { padding: 0; }
        .chat-anchor { width: 100%; }
        i.fa-arrow-right.redirect-arrow{transform: rotate(322deg);}

        .chat-call-main a, .chat-call-main span, .chat-call-main i { color: white; }
        .redirect-arrow { border-radius: 50%; border: 1px solid #fff; width: 40px; height: 40px; display: flex; justify-content: center; align-items: center; }

        /* Testimonials */
        .testimonials-heading h2 { color: var(--white); text-align: center; font-family: 'Satoshi', sans-serif; font-size: 54px; font-weight: 500; line-height: 140%; text-transform: capitalize; }
        .testimonials-heading span { color: var(--white); font-family: "Instrument Serif"; font-size: 54px; font-style: italic; font-weight: 400; line-height: 140%; text-transform: capitalize; }
        .testimonials-heading p { color: var(--para-text); text-align: center; font-family: 'Satoshi', sans-serif; font-size: 18px; font-weight: 500; line-height: 140%; }
        .testimonial-card { padding: 24px; display: flex; flex-direction: column; border-radius: 20px; border: 1px solid rgba(134, 146, 166, 0.20); background: rgba(255, 255, 255, 0.05); box-shadow: 0px 1px 0px 0px rgba(255, 255, 255, 0.08) inset; }
        .stars { color: #ff4444; width: 24px; margin-bottom: 16px; display: flex; gap: 4px; }
        .testimonial-text { color: #D9D9D9; font-family: "Martel Sans"; font-size: 16px; font-weight: 400; line-height: 160%; text-transform: capitalize; }
        .author { display: flex; align-items: center; gap: 12px; margin-top: 16px; }
        .author h2 { color: #F8F8F8; font-family: "Martel Sans"; font-size: 20px; font-weight: 600; line-height: normal; }
        .author-avatar { width: 40px; height: 40px; border-radius: 50%; background: linear-gradient(135deg, #667eea, #764ba2); display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; font-size: 14px; }
        .swiper-pagination { position: relative; margin-top: 24px; }
        .swiper-pagination-bullet { background: #D9D9D9; width: 12px; height: 12px; border-radius: 50%; border: none; opacity: 1; margin: 0 10px; }
        .swiper-pagination-bullet-active { background: transparent; border: 2px solid #F40009; width: 12px; height: 12px; }

        /* Contact */
        .contact-heading h3 { color: var(--white); text-align: center; font-family: 'Satoshi', sans-serif; font-size: 24px; font-weight: 500; line-height: 140%; text-transform: capitalize; }
        .contact-heading h3 span { color: var(--white); font-family: "Instrument Serif"; font-size: 24px; font-style: italic; font-weight: 400; line-height: 140%; text-transform: capitalize; }
        .contact-card {border: 1px solid #D4D4D4;background: linear-gradient(180deg, #333 0%, #1D1D1D 100%);box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.05); padding: 30px; border-radius: 10px;  }
        .contact-heading p { color: var(--white); text-align: center; font-family: "Martel Sans"; font-size: 16px; font-weight: 400; line-height: 25px; }
        .contact-items a { text-decoration: none; color: inherit; transition: all 0.3s ease; display: flex; align-items: center; gap: 15px; padding: 10px 0; }
        .contact-icons { width: 45px; height: 45px; display: flex; align-items: center; justify-content: center; flex-shrink: 0; background-color: #111111; border-radius: 50%; }
        .contact-icons i { color: white; font-size: 18px; }
        .contact-text p { color: #FFF; font-family: "Martel Sans", sans-serif; font-size: 16px; font-weight: 400; line-height: normal; margin: 0; }
        .contact-text p:first-child { font-weight: 600; margin-bottom: 2px; }
        .social-section { margin-top: 50px; }
        .social-title { font-size: 24px; font-weight: 400; margin-bottom: 20px; }
        .social-links { display: flex; gap: 15px; }
        .social-link {border: 1px solid #F40009; width: 36px; height: 36px;  border-radius: 50%; display: flex; align-items: center; justify-content: center; text-decoration: none; color: white; font-size: 20px; transition: all 0.3s ease; }
        .social-link:hover { background-color: rgba(0, 0, 0, 0.5); transform: translateY(-2px); }
        .connacted-heading h3 { color: var(--white); text-align: center; font-family: 'Satoshi', sans-serif; font-size: 54px; font-weight: 500; line-height: 140%; text-transform: capitalize; }
        .connacted-heading h3 span { color: var(--white); font-family: "Instrument Serif"; font-size: 54px; font-style: italic; font-weight: 400; line-height: 140%; text-transform: capitalize; }
        .contact-form input { display: flex; height: 48px; padding: 20px; align-items: center; flex: 1 0 0; border-radius: 6px; border: 1px solid #E5E7EB; background: #FFF; }
        .contact-form textarea { border-radius: 6px; border: 1px solid #E5E7EB; background: #FFF; display: flex; width: 100%; max-width: 810px; height: 200px; padding: 20px; align-items: flex-start; resize: vertical; }
        .red-btn { border:0; display: flex; width: 180px; padding: 12px 0px; justify-content: center; align-items: center; border-radius: 10px; background:#F40009; backdrop-filter: blur(10px); color: white; text-decoration: none; }
        .bg-contact { border-radius: 20px; border: 1px solid rgba(134, 146, 166, 0.20); background: rgba(255, 255, 255, 0.05); box-shadow: 0px 1px 0px 0px rgba(255, 255, 255, 0.08) inset; }

        /* Footer */
        .footer-bg { border-radius: 10px; background: linear-gradient(0deg, #111 0%, #111 100%),  lightgray 50% / cover no-repeat; }
        .footer-logo h2 { color: #FFF; font-family: 'Inter', sans-serif; font-size: 42px; font-weight: 600; line-height: normal; letter-spacing: -2.4px; }
        .footer-content { background: linear-gradient(180deg, #333 0%, #1D1D1D 100%);box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.05); border: 1px solid #D4D4D4;  border-radius: 10px;  padding: 32px; }
        .email-form { display: flex; border-radius: 14px; background: #FFF; width: 100%; max-width: 707px; padding: 12px; align-items: center; gap: 12px; }
        .email-input { display: flex; padding: 14px 16px; align-items: center; gap: 10px; border-radius: 12px; border: 1px solid #99A2A5; background: #FFF; flex: 1 0 0; min-width: 0; }
        .email-btn {border: 0; border-radius: 10px; background: #F40009; display: flex; padding: 12px 48.5px; justify-content: center; color: var(--white); border: 0; align-items: center; backdrop-filter: blur(10px); }
        .footer-bottom { display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid #CCD1D2; }
        .footer-link ul li { padding: 8px 12px; background: var(--light-grey); border-radius: 5px; }
        .footer-link ul li a { text-decoration: none; color: #FFF; font-family: 'Satoshi', sans-serif; font-size: 16px; font-weight: 500; line-height: 26px; }

        /* Footer Toggle Styles */
        .footer-toggler {
            background: none;
            border: 1px solid var(--primary);
            border-radius: 5px;
            padding: 8px 12px;
            color: var(--primary);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .footer-toggler:hover {
            background: var(--primary);
            color: white;
        }
        .footer-toggler:focus {
            box-shadow: 0 0 0 0.25rem rgba(244, 0, 9, 0.25);
            outline: none;
        }
        .footer-toggler-icon i {
            font-size: 18px;
            transition: transform 0.3s ease;
        }

        /* Footer Navigation Animation */
        #footerNav {
            transition: all 0.3s ease;
        }

        .footer-nav-list {
            transition: all 0.3s ease;
        }

        /* Hide footer toggle on large screens */
        @media (min-width: 992px) {
            .footer-toggler {
                display: none !important;
            }
        }
        .footer-copyright p { color: #B1B1B1; font-family: Poppins; font-size: 18px; font-weight: 400; line-height: normal; text-transform: capitalize; }
        .social-content { display: flex; gap: 10px; }
        .social-btn { width: 36px; height: 36px; border-radius: 50%; display: flex; align-items: center; justify-content: center; text-decoration: none; color: white; border: 1px solid #F40009;  font-size: 20px; transition: all 0.3s ease; }
        .service-icon i { font-size: 25px; color: red; width: 70px; height: 70px; border-radius: 50%; border: 1px solid rgba(255, 255, 255, 0.10); background: rgba(255, 255, 255, 0.10); box-shadow: 0px 0px 29px 0px rgba(244, 0, 9, 0.20); display: flex; justify-content: center; align-items: center; }

        /*---responsiveness--*/
        @media (max-width: 1200px) {
            .container { padding: 0 20px; }
            .email-form { width: 100%; max-width: 500px; }
        }

        @media (max-width: 1180px){
            .hero h1 {font-size: 50px;}
            .hero h1 span {font-size: 50px;}
            .about-heading {font-size: 40px;}
            .about-heading span {font-size: 40px;}
            .communicate-heading h3 {font-size: 40px; width: 100%;}
            .communicate-heading h3 span{font-size: 40px;}
            .testimonials-heading h2 {font-size: 40px;}
            .testimonials-heading h2 span{font-size: 40px;}
            .connacted-heading h3 {font-size: 40px;}
            .connacted-heading h3 span {font-size: 40px;}
        }

        @media (max-width: 992px) {
            .para-text{width: 100%}
            .hero h1{width: 100% !important;}
            .hero { padding: 120px 0 80px; }
            .about { padding-block: 80px; }
            .mission-sec { padding-block: 80px; }
            .testimonials-heading p { width: 100% !important; }
            .communicate-heading p { width: 100% !important; }
            .footer-content { flex-direction: column; gap: 20px; text-align: center; }
            .footer-bottom { flex-direction: column; gap: 20px; text-align: center; }
            .footer-copyright { flex-direction: column; gap: 15px; text-align: center; }
            .footer-nav-container { width: 100%; }
            .footer-nav-list { flex-direction: column; gap: 10px !important; margin-top: 15px; }
            .footer-nav-list li { width: 100%; text-align: center; }
        }

        @media (max-width: 768px) {
            .hero h1 { font-size: 36px; }
            .hero h1 span { font-size: 36px; }
            .about-heading { font-size: 32px; }
            .about-heading span { font-size: 32px; }
            .communicate-heading h3 { font-size: 32px; }
            .communicate-heading h3 span { font-size: 32px; }
            .testimonials-heading h2 { font-size: 32px; }
            .testimonials-heading h2 span { font-size: 32px; }
            .connacted-heading h3 { font-size: 32px; }
            .connacted-heading h3 span { font-size: 32px; }
            .para-text { font-size: 16px; }
            .about-para { font-size: 16px; }
            .service-para { font-size: 16px; }
            .list-content { font-size: 24px; }
            .contact-form textarea { width: 100%; }
            .email-form { flex-direction: column; padding: 16px; }
            .email-input { margin-bottom: 12px; }
            .nav-item { margin-bottom: 8px; }
            .footer-toggler { display: block !important; }
            .footer-nav-list { gap: 8px !important; }
            .image-container { margin-top: 30px; }
        }

        @media (max-width: 576px) {
            .hero { padding: 100px 0 60px; }
            .hero h1 { font-size: 28px; }
            .hero h1 span { font-size: 28px; }
            .about-heading { font-size: 28px; }
            .about-heading span { font-size: 28px; }
            .communicate-heading h3 { font-size: 28px; }
            .communicate-heading h3 span { font-size: 28px; }
            .testimonials-heading h2 { font-size: 28px; }
            .testimonials-heading h2 span { font-size: 28px; }
            .connacted-heading h3 { font-size: 28px; }
            .connacted-heading h3 span { font-size: 28px; }
            .started_btn, .influencer_btn { padding: 10px 24px; font-size: 14px; }
            .service-heading { font-size: 20px; }
            .card-custom h5 { font-size: 22px; }
            .list-content { font-size: 20px; }
            .contact-heading h3 { font-size: 20px; }
            .contact-heading h3 span { font-size: 20px; }
            .footer-logo h2 { font-size: 28px; }
            .about { padding-block: 60px; }
            .mission-sec { padding-block: 60px; }
            .container { padding: 0 15px; }
        }

        @media (max-width: 480px) {
            .hero h1 { font-size: 24px; line-height: 1.3; }
            .hero h1 span { font-size: 24px; }
            .para-text { font-size: 14px; }
            .started_btn, .influencer_btn { width: 100%; margin-bottom: 10px; }
            .d-flex.justify-content-center.gap-3 { flex-direction: column; align-items: center; }
            .service-heading { font-size: 18px; }
            .service-para { font-size: 14px; }
            .about-para { font-size: 14px; }
            .card-custom p { font-size: 16px; }
            .list-content { font-size: 18px; }
            .testimonial-text { font-size: 14px; }
            .author h2 { font-size: 16px; }
            .contact-text p { font-size: 14px; }
            .footer-copyright p { font-size: 14px; }
            .video-container { margin-top: 20px; }
            .video-play-icon { width: 50px; height: 50px; font-size: 20px; }
            .video-container:hover { transform: none; } /* Disable hover effects on mobile */
        }


    </style>
</head>

<body>
<!-- Header -->
<header>
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="#home">
                <img src="{{asset('website/assets/landing_images/header.png')}}" alt="" height="30" class="d-inline-block align-text-top me-2">
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto gap-3">
                    <li class="nav-item"><a class="nav-link" href="#home">Home</a></li>
                    <li class="nav-item"><a class="nav-link" href="#about-us">Influencers</a></li>
                    <li class="nav-item"><a class="nav-link" href="#mission">About Us</a></li>
                    <li class="nav-item"><a class="nav-link" href="#Services">Services</a></li>
                    <li class="nav-item"><a class="nav-link" href="#Testimonials">Testimonials</a></li>
                    <li class="nav-item"><a class="nav-link" href="#contact">Contact Us</a></li>
                </ul>
            </div>
        </div>
    </nav>
</header>


<!-- Hero Section -->
<section id="home" class="hero">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-12">

                <h1 class="w-75 m-auto">Guided Support for <span class="highlight">Growing</span> Your <span
                            class="highlight">Influence</span> Professionally</h1>
                <p class="para-text mt-4 mb-4">Connect, grow, and monetize your influence like never before. Whether
                    you're just starting out or a seasoned content creator, our platform helps you shine, get
                    discovered, and work with the right brands.</p>
                <div class="d-flex justify-content-center gap-3">
                    <a href="#contact" class="started_btn">Get Started </a>
                    <a href="#" class="influencer_btn">Join As Influencer</a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Services -->
<section id="services">
    <div class="container">
        <div class="row g-4">
            <div class="col-lg-4">
                <div class="card h-100 text-center">
                    <div class="card-body text-start">
                        <div class="service-icon pb-4">
                            <i class="fa-solid fa-phone" style="color: #ff0000;"></i>
                        </div>
                        <h2 class="service-heading pb-3">Morning kick-off Calls</h2>
                        <p class="service-para">We create with purpose, respecting people and the planet.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card h-100 text-center">
                    <div class="card-body text-start">
                        <div class="service-icon pb-4">
                            <i class="fas fa-user-shield" style="color: red;"></i>
                        </div>
                        <h2 class="service-heading pb-3">1-on-1 Voice and Video Calls</h2>
                        <p class="service-para">We create with purpose, respecting people and the planet.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card h-100 text-center">
                    <div class="card-body text-start">
                        <div class="service-icon pb-4">
                            <i class="fas fa-user-shield" style="color: red;"></i>
                        </div>
                        <h2 class="service-heading pb-3">Virtual Consultancy Sessions</h2>
                        <p class="service-para">We create with purpose, respecting people and the planet.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- About Section -->
<section class="about" id="about-us">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">

                <h2 class="about-heading pb-3">Built For <span>Influencers</span>, By Influencers</h2>
                <p class="mb-4 about-para">Connect, grow, and monetize your influence like never before. Whether
                    you're just
                    starting out or a seasoned content creator, our platform helps you shine, get discovered, and
                    work with the right brands.Connect, grow, and monetize your influence like never before. Whether
                    you're just starting out or a seasoned content creator, our platform helps you shine, get
                    discovered, and work with the right brands.Connect, grow, and monetize your influence like never
                    before. Whether you're just starting out or a seasoned content creator, our platform helps you
                    shine, get discovered, and work with the right brands.</p>
            </div>
            <div class="col-lg-6">
                <div class="image-container">
                    <img src="{{asset('website/assets/landing_images/Frame 1707479359.png')}}" alt="About Us Image" class="about-image">
                </div>
            </div>
        </div>
    </div>
</section>
<section class="container text-center py-5 mission-sec " id="mission">

    <h2 class="about-heading">Our Mission</h2>
    <p class="mx-auto mission-heading about-para">
        Connect, grow, and monetize your influence like never before. Whether you're just starting out or a seasoned
        content creator
    </p>

    <div class="row justify-content-center mt-5 g-4">
        <div class="col-md-5">
            <div class="card-custom text-start h-100">
                <img src="{{asset('website/assets/landing_images/Video.png')}}" alt="card img">
                <div class="py-3">
                    <h5>Meaningful Connections, Real Opportunities</h5>
                    <p>Connect, grow, and monetize your influence like never before. Whether you're just starting
                        out or a seasoned content creator</p>
                </div>
            </div>
        </div>
        <div class="col-md-5">
            <div class="card-custom text-start h-100">
                <img src="{{asset('website/assets/landing_images/mission.png')}}" alt="card img">
                <div class="py-3">
                    <h5>Your Growth, Our Priority</h5>
                    <p>Connect, grow, and monetize your influence like never before. Whether you're just starting
                        out or a seasoned content creator</p>
                </div>
            </div>
        </div>
    </div>
</section>
<section class="communication-sec text-center pt-5" id="Services">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">

                <div class="communicate-heading  m-auto">
                    <h3>Everything You <span>Need</span> to Elevate Your Influence</h3>
                    <p class="w-75 m-auto">Connect, grow, and monetize your influence like never before. Whether
                        you're just starting
                        out or a seasoned content creator</p>
                </div>
                <div class=" py-3 chat-call-main">
                    <ul class="list-group py-5">
                        <li class="list-group-item py-3">
                            <a href="chat-url"
                               class="chat-anchor d-flex justify-content-between align-items-center text-decoration-none">
                                <div class="list-content"><span class="list-number">01.</span> Chats</div>
                                <i class="fa-solid fa-arrow-right redirect-arrow"></i>
                            </a>
                        </li>

                        <li class="list-group-item py-3">
                            <a href="chat-url"
                               class="chat-anchor d-flex justify-content-between align-items-center text-decoration-none">
                                <div class="list-content"><span class="list-number">02.</span> Call</div>
                                <i class="fa-solid fa-arrow-right redirect-arrow"></i>
                            </a>
                        </li>
                        <li class="list-group-item py-3">
                            <a href="chat-url"
                               class="chat-anchor d-flex justify-content-between align-items-center text-decoration-none">
                                <div class="list-content"><span class="list-number">03.</span> Video Calls</div>
                                <i class="fa-solid fa-arrow-right redirect-arrow"></i>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>
<section class="testimonials-sec text-center pt-5 " id="Testimonials">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">

                <div class="testimonials-heading">
                    <h2>What Users Are <span>Saying</span></h2>
                    <p class="w-50 m-auto mb-5">Connect, grow, and monetize your influence like never before.
                        Whether
                        you're just starting
                        out or a seasoned content creator</p>
                </div>

                <div class="swiper">
                    <div class="swiper-wrapper">
                        <div class="swiper-slide">
                            <div class="testimonial-card text-start">
                                <div class="stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <div class="testimonial-text">We Guide Organizations Through Their Digital Journey,
                                    Optimizing. We Guide Organizations Through Their Digital Journey, Optimizing.
                                </div>
                                <div class="author">
                                    <div class="author-avatar">MW</div>
                                    <h2 class="m-0">Matthew Ward</h2>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide">
                            <div class="testimonial-card text-start">
                                <div class="stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <div class="testimonial-text">Outstanding service and expertise! Their team
                                    transformed our digital presence completely. Highly recommend their innovative
                                    approach.</div>
                                <div class="author">
                                    <div class="author-avatar">JS</div>
                                    <h2 class="m-0">Matthew Ward</h2>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide">
                            <div class="testimonial-card text-start">
                                <div class="stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <div class="testimonial-text">Professional, reliable, and results-driven. They
                                    delivered exactly what they promised and exceeded our expectations.</div>
                                <div class="author">
                                    <div class="author-avatar">SD</div>
                                    <h2 class="m-0">Matthew Ward</h2>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide">
                            <div class="testimonial-card text-start">
                                <div class="stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <div class="testimonial-text">Incredible experience from start to finish. The team
                                    understood our vision perfectly and delivered real business impact.</div>
                                <div class="author">
                                    <div class="author-avatar">EM</div>
                                    <h2 class="m-0">Matthew Ward</h2>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="swiper-pagination"></div>
                </div>

            </div>
        </div>
    </div>
</section>
<section class="contact-sec py-5 my-5" id="contact">
    <div class="container">
        <div class="row p-4 bg-contact">
            <div class="col-lg-5 col-md-12 col-sm-12">
                <div class="contact-card contact-heading ">
                    <h3 class="text-start">What Users Are <span> Saying</span></h3>
                    <p class="text-start">Learn modern web development with hands-on projects. Covering HTML,
                        CSS, JavaScript, React, Node.js, and more.</p>

                    <div class="contact-items ">
                        <a href="tel:+27217857227" class="d-flex gap-4">
                            <div class="contact-icons">
                                <svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 36 37"
                                     fill="none">
                                    <path
                                            d="M14.5036 9.54461L16.0239 13.1933C16.2899 13.8014 16.1379 14.5235 15.6058 14.9416L13.7435 16.4998C14.9977 19.1603 17.1641 21.3267 19.8246 22.5809L21.3828 20.7186C21.8009 20.1865 22.523 20.0345 23.1312 20.3005L26.7798 21.8208C27.5019 22.0868 27.844 22.885 27.654 23.6071L26.7418 26.9517C26.5518 27.5978 25.9817 28.0539 25.2975 28.0539C15.8719 28.0539 8.27051 20.4525 8.27051 11.0269C8.27051 10.3428 8.72659 9.77265 9.3727 9.58262L12.7173 8.67045C13.4394 8.48042 14.2376 8.82248 14.5036 9.54461Z"
                                            fill="white" />
                                </svg>
                            </div>
                            <div class="contact-text ">
                                <p class="m-0 text-start">Phone</p>
                                <p class="m-0 text-start ">+ 27 21 785 7227</p>
                            </div>
                        </a>
                    </div>

                    <div class="contact-items">
                        <a href="mailto:<EMAIL>" class="d-flex gap-4">
                            <div class="contact-icons">
                                <i class="fa-solid fa-envelope"></i>
                            </div>
                            <div class="contact-text">
                                <p class="m-0 text-start">Email</p>
                                <p class="m-0 text-start">info@vivolinks</p>
                            </div>
                        </a>
                    </div>

                    <div class="contact-items ">
                        <a href="#" target="_blank" class="d-flex gap-4">
                            <div class="contact-icons">
                                <i class="fa-solid fa-location-dot"></i>
                            </div>
                            <div class="contact-text">
                                <p class="m-0 text-start">Address</p>
                                <p class="m-0 text-start">Road,Rosebank,Cape Town 7700</p>
                            </div>
                        </a>
                    </div>

                    <div class="social-section">
                        <h2 class="social-title">Social Links</h2>
                        <div class="social-links">
                            <a href="#" class="social-link">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="#" class="social-link">
                                <i class="fab fa-facebook"></i>
                            </a>
                            <a href="#" class="social-link">
                                <i class="fab fa-linkedin"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="contact-form col-lg-7 col-md-12 col-sm-12">
                <form id="contactForm" action="https://vivo-link.democustomprojects.com/contact-us" method="POST"
                      class="contact-form">
                    <input type="hidden" name="_token" value="Y554JPpsmkmNOMYTK9ucQ4vEScBNz4u80GqGvVKd"
                           autocomplete="off">
                    <div class="connacted-heading py-3">
                        <h3 class="text-start">I Want to <span>Be Contacted</span> </h3>
                    </div>
                    <div class="row row-gap-11">
                        <div class="col-lg-6 col-md-6 mb-3">
                            <input placeholder="First Name" type="text" name="first_name" value=""
                                   class="w-100 form-control  ">
                        </div>
                        <div class="col-lg-6 col-md-6  mb-3">
                            <input placeholder="Last Name" type="text" name="last_name" value=""
                                   class="w-100 form-control  ">
                        </div>
                        <div class="col-lg-6 col-md-6  mb-3">
                            <input placeholder="Email Address" type="email" name="email" value=""
                                   class="w-100 form-control  ">
                        </div>
                        <div class="col-lg-6 col-md-6  mb-3">
                            <input placeholder="Phone Number" type="number" name="phone_number" value=""
                                   class="w-100 form-control  ">
                        </div>
                        <div class="col-lg-12">
                            <textarea class="w-100 form-control  " name="message" placeholder="Message"></textarea>
                        </div>

                        <div class="col-lg-12 mt-3">
                            <button type="submit" class="red-btn">Submit</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>
<footer class="footer-sec mb-5">
    <div class="container footer-bg p-5">
        <div class="row">
            <div class="col-lg-12">
                <div class="footer-content d-flex justify-content-between align-items-center mb-5">
                    <div class="footer-logo">
                        <h2>Subscribe Newsletter</h2>
                    </div>
                    <div class="email-form">
                        <input type="email" class="email-input" placeholder="Enter your Email">
                        <button class="email-btn">Get Started</button>
                    </div>

                </div>

                <div class="footer-bottom pb-5 mb-3">
                    <div class="footer-nav-container d-flex justify-content-between align-items-center">
                        <div class="footer-img">
                            <img src="./assets/images/header.png" alt="">
                        </div>
                        <button class="footer-toggler d-lg-none" type="button" data-bs-toggle="collapse" data-bs-target="#footerNav" aria-controls="footerNav" aria-expanded="false" aria-label="Toggle footer navigation">
                            <span class="footer-toggler-icon">
                                <i class="fas fa-bars"></i>
                            </span>
                        </button>
                    </div>
                    <div class="footer-link">
                        <div class="collapse d-lg-block" id="footerNav">
                            <ul class="list-unstyled d-flex gap-4 footer-nav-list">
                                <li><a href="#home">Home</a></li>
                                <li><a href="#about-us">Influencers</a></li>
                                <li><a href="#mission">About Us</a></li>
                                <li><a href="#Services">Services</a></li>
                                <li><a href="#Testimonials">Testimonials</a></li>
                                <li><a href="#contact">Contact Us</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="footer-copyright d-flex justify-content-between align-items-center">
                    <div>
                        <p>Copyright 2023 © BrightDesk AI All rights reserved</p>
                    </div>
                    <div class="social-icons">
                        <div class="social-content">
                            <a href="#" class="social-btn">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="#" class="social-btn">
                                <i class="fab fa-facebook"></i>
                            </a>
                            <a href="#" class="social-btn">
                                <i class="fab fa-linkedin"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</footer>
<script src="https://cdnjs.cloudflare.com/ajax/libs/Swiper/11.0.5/swiper-bundle.min.js"></script>
<script>
    new Swiper('.swiper', {
        slidesPerView: 1,
        spaceBetween: 16,
        loop: true,
        autoplay: { delay: 5000 },
        breakpoints: {
            480: {
                slidesPerView: 1.2,
                spaceBetween: 16
            },
            640: {
                slidesPerView: 1.5,
                spaceBetween: 20
            },
            768: {
                slidesPerView: 2,
                spaceBetween: 24
            },
            1024: {
                slidesPerView: 2.5,
                spaceBetween: 24
            }
        },
        pagination: {
            el: '.swiper-pagination',
            clickable: true,
        }
    });
</script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
<script>
    // Smooth scrolling
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                const headerHeight = 80;
                const targetPosition = target.offsetTop - headerHeight;
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });



    // Contact form
    document.getElementById('contactForm').addEventListener('submit', function (e) {
        e.preventDefault();
        const button = this.querySelector('button[type="submit"]');
        const originalText = button.textContent;
        button.textContent = 'Sending...';
        button.disabled = true;

        setTimeout(() => {
            alert('🎉 Thank you! Our team will contact you within 24 hours.');
            this.reset();
            button.textContent = originalText;
            button.disabled = false;
        }, 2000);
    });

    // Auto-advance carousel
    const carousel = new bootstrap.Carousel(document.querySelector('#testimonialCarousel'), {
        interval: 5000,
        wrap: true
    });

    // Footer toggle functionality
    const footerToggler = document.querySelector('.footer-toggler');
    const footerNav = document.querySelector('#footerNav');

    if (footerToggler && footerNav) {
        footerToggler.addEventListener('click', function() {
            const icon = this.querySelector('i');
            const isExpanded = this.getAttribute('aria-expanded') === 'true';

            // Toggle icon
            if (isExpanded) {
                icon.classList.remove('fa-times');
                icon.classList.add('fa-bars');
            } else {
                icon.classList.remove('fa-bars');
                icon.classList.add('fa-times');
            }
        });

        // Listen for Bootstrap collapse events
        footerNav.addEventListener('hidden.bs.collapse', function() {
            const icon = footerToggler.querySelector('i');
            icon.classList.remove('fa-times');
            icon.classList.add('fa-bars');
        });

        footerNav.addEventListener('shown.bs.collapse', function() {
            const icon = footerToggler.querySelector('i');
            icon.classList.remove('fa-bars');
            icon.classList.add('fa-times');
        });
    }
</script>
</body>

</html>

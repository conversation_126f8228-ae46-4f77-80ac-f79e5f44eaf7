<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Influence - Professional Influence Marketing Platform</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">


    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Swiper/11.0.5/swiper-bundle.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
            href="https://fonts.googleapis.com/css2?family=Instrument+Serif:ital,wght@0,400;0,600;1,400&family=Satoshi:ital,wght@0,300;0,400;0,500;0,600;0,700;0,900;1,300;1,400;1,500;1,600;1,700;1,900&display=swap"
            rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Martel+Sans:wght@200;300;400;600;700;800;900&display=swap"
          rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">


    <style>


       

       

    </style>
</head>

<body>
<!-- Header -->
<header>
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top">
        <div class="container">
            <a class="navbar-brand" href="#home">
                <img src="{{asset('website/assets/landing_images/header.png')}}" alt="" height="30" class="d-inline-block align-text-top me-2">
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto gap-3">
                    <li class="nav-item"><a class="nav-link" href="#home">Home</a></li>
                    <li class="nav-item"><a class="nav-link" href="#about-us">Influencers</a></li>
                    <li class="nav-item"><a class="nav-link" href="#mission">About Us</a></li>
                    <li class="nav-item"><a class="nav-link" href="#Services">Services</a></li>
                    <li class="nav-item"><a class="nav-link" href="#Testimonials">Testimonials</a></li>
                    <li class="nav-item"><a class="nav-link" href="#contact">Contact Us</a></li>
                </ul>
            </div>
        </div>
    </nav>
</header>


<!-- Hero Section -->
<section id="home" class="hero">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-12">

                <h1 class="w-75 m-auto">Your <span class="highlight">Influence.</span>Your <span
                            class="highlight"> Rules.</span></h1>
                <p class="para-text mt-4 mb-4">Connect with fans, grow your presence, and get paid, all in one place.
                    Whether you’re just starting or already thriving, VivoLink puts your influence in your hands.</p>
                <div class="d-flex justify-content-center gap-3">
                    <a href="#contact" class="started_btn">Get Started </a>
                    <a href="#contact" class="influencer_btn">Join As Influencer</a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Services -->
<section id="services">
    <div class="container">
        <div class="row g-4">
            <div class="col-lg-4">
                <div class="card h-100 text-center">
                    <div class="card-body text-start">
                        <div class="service-icon mb-4">
                            <img src="{{asset('website/assets/landing_images/Group 1321314926.svg')}}" alt="Morning kick-off Calls">
                        </div>
                        <h2 class="service-heading pb-3">Morning kick-off Calls</h2>
                        <p class="service-para">Start your day with direction, Morning Kick-Off Calls give you a space to reset, refocus, and
                            move forward with intention.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card h-100 text-center">
                    <div class="card-body text-start">
                        <div class="service-icon mb-4">
                            <img src="{{asset('website/assets/landing_images/Group 1321314927.svg')}}" alt="1-on-1 Voice and Video Calls">
                        </div>
                        <h2 class="service-heading pb-3">1-on-1 Voice and Video Calls</h2>
                        <p class="service-para">Get direct access. Ask questions, get advice, or just talk — 1-on-1, your way.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card h-100 text-center">
                    <div class="card-body text-start">
                        <div class="service-icon mb-4">
                            <img src="{{asset('website/assets/landing_images/Group 1321314925.svg')}}" alt="Virtual Consultancy Sessions">
                        </div>
                        <h2 class="service-heading pb-3">Virtual Consultancy Sessions</h2>
                        <p class="service-para">Skip the generic tips. Get personal, practical guidance from someone who gets it.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- About Section -->
<section class="about" id="about-us">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h2 class="about-heading pb-3">Shaped by the People Who Use It</h2>
                <p class="mb-4 about-para">VivoLink isn’t built from the outside looking in, it’s shaped by the creators who live this
                    every day. From day one, we’ve listened, adapted, and evolved based on real feedback
                    from real influencers. What matters to you, freedom, control, connection, getting paid for
                    your time, is what drives every decision we make. This platform is built with purpose, not
                    guesswork. And it’ll keep growing with you, not ahead of you.
                </p>
            </div>
            <div class="col-lg-6">
                <div class="image-container">
                    <img src="{{asset('website/assets/landing_images/person-img.png')}}" alt="Richard Torres - CEO Of Bright Corp." class="about-image">
                    <div class="image-text-overlay">
                        <h3 class="overlay-name">Richard Torres</h3>
                        <p class="overlay-title">CEO Of Bright Corp.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<section class="container text-center py-5 mission-sec " id="mission">

    <h2 class="about-heading">Our Mission</h2>
    <p class="mx-auto mission-heading about-para">
        To make connection personal again.
        VivoLink exists to break down the walls between creators and their audiences, to make it
        easy, valuable, and real. This isn’t about algorithms or clout. It’s about giving creators the
        tools to take control of their influence, earn directly, and build something that lasts.
        We’re here to listen, adapt, and build with the people who actually use the platform.
        That’s the mission. It always will be
    </p>

    <div class="row justify-content-center mt-5 g-4">
        <div class="col-md-5">
            <div class="card-custom text-start h-100">
                <img src="{{asset('website/assets/landing_images/image 66.png')}}" alt="card img">
                <div class="py-3">
                    <h5>Meaningful Connections, Real Opportunities</h5>
                    <p>VivoLink is built to create value for both you and your followers. It’s about forming genuine
                        connections that inspire growth, open doors, and bring real opportunities, for everyone
                        involved. Whether you’re just starting or already established, this is your platform to grow
                        and make a difference.</p>
                </div>
            </div>
        </div>
        <div class="col-md-5">
            <div class="card-custom text-start h-100">
                <img src="{{asset('website/assets/landing_images/growth.png')}}" alt="card img">
                <div class="py-3">
                    <h5>Your Growth, Our Priority</h5>
                    <p>Your journey is unique, and we’re here to support it. With tools and opportunities built
                        around you, VivoLink helps you develop your presence steadily, confidently, and your way</p>
                </div>
            </div>
        </div>
    </div>
</section>
<section class="communication-sec text-center pt-5" id="Services">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">

                <div class="communicate-heading m-auto mb-5 scroll-animate">
                    <h3>Everything You <span>Need</span> to Elevate Your Influence</h3>
                    <p class="w-75 m-auto">Connect, grow, and monetize your influence like never before. Whether you’re just starting
                        out or a seasoned content creator, VivoLink gives you the tools to build authentic
                        relationships and expand your reach.
                    </p>
                </div>
                <div class="services-container text-start">
                    <!-- Service 1: Chats -->
                    <div class="service-item scroll-animate">
                        <div class="service-header" onclick="toggleService(1)">
                            <span class="service-number">01.</span>
                            <i class="fas fa-comments " style="color: #f40009; margin-right: 8px;"></i>
                            <span class="service-title">Chats</span>
                            <i class="fas fa-chevron-down service-toggle"></i>
                        </div>
                        <div class="service-content" id="service-1">
                            <div class="service-description">
                                Instant messaging that keeps you connected and engaged with your community.
                            </div>
                        </div>
                    </div>

                    <!-- Service 2: Calls -->
                    <div class="service-item scroll-animate">
                        <div class="service-header" onclick="toggleService(2)">
                            <span class="service-number">02.</span>
                            <i class="fas fa-phone " style="color: #f40009; margin-right: 8px;"></i>
                            <span class="service-title">Calls</span>
                            <i class="fas fa-chevron-down service-toggle"></i>
                        </div>
                        <div class="service-content" id="service-2">
                            <div class="service-description">
                                Personal voice calls for deeper, one-on-one conversations and advice.
                            </div>
                        </div>
                    </div>

                    <!-- Service 3: Video Calls -->
                    <div class="service-item scroll-animate">
                        <div class="service-header" onclick="toggleService(3)">
                            <span class="service-number">03.</span>
                            <i class="fas fa-video " style="color: #f40009; margin-right: 8px;"></i>
                            <span class="service-title">Video Calls</span>
                            <i class="fas fa-chevron-down service-toggle"></i>
                        </div>
                        <div class="service-content" id="service-3">
                            <div class="service-description">
                                Face-to-face interactions that bring your connections to life.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<section class="testimonials-sec text-center pt-5 " id="Testimonials">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">

                <div class="testimonials-heading scroll-animate">
                    <h2>Trusted by Creators <span>Just Like You</span></h2>
                    <p class="w-75 m-auto mb-5">Connect, grow, and monetize your influence like never before. Whether you're just starting
                        out or a seasoned content creator, VivoLink is built with creators in mind, designed to help
                        you take control, build real connections, and unlock your full potential.
                        Join a growing community of creators shaping the future of influence, your journey starts
                        here.</p>
                </div>

                <div class="swiper scroll-animate">
                    <div class="swiper-wrapper">
                        <div class="swiper-slide">
                            <div class="testimonial-card text-start ">
                                <div class="testimonial-text">No matter where you're at in your journey, VivoLink is made to help you
                                    grow, connect, and monetize on your terms. Join a community of creators
                                    who are redefining what influence means — and be part of something real.</div>
                                <div class="author">
                                    <div class="author-avatar">VL</div>
                                    <h2 class="m-0">VivoLink Creator</h2>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide">
                            <div class="testimonial-card text-start ">
                                <div class="testimonial-text">From fresh starters to seasoned pros, VivoLink supports creators with tools
                                    and opportunities designed to help you build meaningful connections and
                                    sustainable growth. Step into the future of influence with us.</div>
                                <div class="author">
                                    <div class="author-avatar">VL</div>
                                    <h2 class="m-0">VivoLink Creator</h2>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide">
                            <div class="testimonial-card text-start ">
                                <div class="testimonial-text">Your influence is powerful, and VivoLink gives you the platform to amplify it,
                                    authentically and successfully. Join like-minded creators who are taking
                                    control of their growth and making their mark.</div>
                                <div class="author">
                                    <div class="author-avatar">VL</div>
                                    <h2 class="m-0">VivoLink Creator</h2>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide">
                            <div class="testimonial-card text-start h-100">
                                <div class="testimonial-text">At VivoLink, we believe every creator deserves a platform that respects
                                    their journey. Whether you're just starting out or already established, join a
                                    community focused on real growth and genuine connections.</div>
                                <div class="author">
                                    <div class="author-avatar">VL</div>
                                    <h2 class="m-0">VivoLink Creator</h2>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide">
                            <div class="testimonial-card text-start h-100">
                                <div class="testimonial-text">VivoLink is more than a platform, it's a movement. Connect with creators
                                    who share your passion, discover new opportunities, and build your influence
                                    the way you want.</div>
                                <div class="author">
                                    <div class="author-avatar">VL</div>
                                    <h2 class="m-0">VivoLink Creator</h2>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide">
                            <div class="testimonial-card text-start h-100">
                                <div class="testimonial-text">Creators like you inspire us every day. That's why VivoLink is designed to
                                    help you expand your reach, deepen connections, and turn your influence
                                    into lasting success.</div>
                                <div class="author">
                                    <div class="author-avatar">VL</div>
                                    <h2 class="m-0">VivoLink Creator</h2>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide">
                            <div class="testimonial-card text-start h-100">
                                <div class="testimonial-text">Your story matters. VivoLink empowers you to connect personally, build
                                    authentic relationships, and grow your influence with the support of a
                                    community that's got your back.</div>
                                <div class="author">
                                    <div class="author-avatar">VL</div>
                                    <h2 class="m-0">VivoLink Creator</h2>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide">
                            <div class="testimonial-card text-start h-100">
                                <div class="testimonial-text">Built for creators who want more than just followers. VivoLink helps you
                                    build meaningful connections, unlock opportunities, and shape your future
                                    on your terms.</div>
                                <div class="author">
                                    <div class="author-avatar">VL</div>
                                    <h2 class="m-0">VivoLink Creator</h2>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-slide">
                            <div class="testimonial-card text-start h-100">
                                <div class="testimonial-text">Join a growing network of creators who are redefining success. With
                                    VivoLink, you get the tools and support to turn your passion into a thriving
                                    influence.</div>
                                <div class="author">
                                    <div class="author-avatar">VL</div>
                                    <h2 class="m-0">VivoLink Creator</h2>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="swiper-pagination"></div>
                </div>

            </div>
        </div>
    </div>
</section>
<section class="contact-sec py-5 my-5" id="contact">
    <div class="container">
        <div class="row p-4 bg-contact">
            <div class="col-lg-5 col-md-12 col-sm-12">
                <div class="contact-card contact-heading scroll-animate h-100">
                    <h3 class="text-start">Want to be <span>involved?</span></h3>
                    <p class="text-start">Our founder is excited to connect with passionate creators like you. When you reach out,
                        expect a personal message straight from them, because your journey matters, and we're
                        looking forward to building the future together.</p>

                    <div class="contact-items">
                        <a href="mailto:<EMAIL>" class="d-flex gap-4">
                            <div class="contact-icons">
                                <i class="fa-solid fa-envelope"></i>
                            </div>
                            <div class="contact-text">
                                <p class="m-0 text-start">Email</p>
                                <p class="m-0 text-start"><EMAIL></p>
                            </div>
                        </a>
                    </div>

                    <div class="social-section">
                        <h2 class="social-title">Official Instagram</h2>
                        <div class="social-links">
                            <a href="https://www.instagram.com/vivolink.me/" target="_blank" class="social-link">
                                <i class="fab fa-instagram"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="contact-form col-lg-7 col-md-12 col-sm-12">
                <form id="contactForm" action="https://vivo-link.democustomprojects.com/contact-us" method="POST"
                      class="contact-form">
                    <input type="hidden" name="_token" value="Y554JPpsmkmNOMYTK9ucQ4vEScBNz4u80GqGvVKd"
                           autocomplete="off">
                    <div class="connacted-heading py-3">
                        <h3 class="text-start">I Want to <span>Be Contacted</span> </h3>
                    </div>
                    <div class="row row-gap-11">
                        <div class="col-lg-6 col-md-6 mb-3">
                            <input placeholder="First Name" type="text" name="first_name" value=""
                                   class="w-100 form-control  ">
                        </div>
                        <div class="col-lg-6 col-md-6  mb-3">
                            <input placeholder="Last Name" type="text" name="last_name" value=""
                                   class="w-100 form-control  ">
                        </div>
                        <div class="col-lg-6 col-md-6  mb-3">
                            <input placeholder="Email Address" type="email" name="email" value=""
                                   class="w-100 form-control  ">
                        </div>
                        <div class="col-lg-6 col-md-6  mb-3">
                            <input placeholder="Phone Number" type="number" name="phone_number" value=""
                                   class="w-100 form-control  ">
                        </div>
                        <div class="col-lg-12">
                            <textarea class="w-100 form-control  " name="message" placeholder="Message"></textarea>
                        </div>

                        <div class="col-lg-12 mt-3">
                            <button type="submit" class="red-btn">Submit</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>
<footer class="footer-sec mb-5">
    <div class="container footer-bg p-5">
        <div class="row">
            <div class="col-lg-12">
                <div class="footer-content d-flex justify-content-between align-items-center mb-5">
                    <div class="footer-logo">
                        <h2>Subscribe Newsletter</h2>
                    </div>
                    <div class="email-form">
                        <input type="email" class="email-input" placeholder="Enter your Email">
                        <button class="email-btn">Get Started</button>
                    </div>

                </div>

                <div class="footer-bottom pb-5 mb-3">
                    <div class="footer-nav-container d-flex justify-content-between align-items-center">
                        <div class="footer-img">
                            <img src="./assets/images/header.png" alt="">
                        </div>
                        <button class="footer-toggler d-lg-none" type="button" data-bs-toggle="collapse" data-bs-target="#footerNav" aria-controls="footerNav" aria-expanded="false" aria-label="Toggle footer navigation">
                            <span class="footer-toggler-icon">
                                <i class="fas fa-bars"></i>
                            </span>
                        </button>
                    </div>
                    <div class="footer-link">
                        <div class="collapse d-lg-block" id="footerNav">
                            <ul class="list-unstyled d-flex gap-4 footer-nav-list">
                                <li><a href="#home">Home</a></li>
                                <li><a href="#about-us">Influencers</a></li>
                                <li><a href="#mission">About Us</a></li>
                                <li><a href="#Services">Services</a></li>
                                <li><a href="#Testimonials">Testimonials</a></li>
                                <li><a href="#contact">Contact Us</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="footer-copyright d-flex justify-content-between align-items-center">
                    <div>
                        <p>Copyright 2023 © BrightDesk AI All rights reserved</p>
                    </div>
                    <div class="social-icons">
                        <div class="social-content">
                            <a href="#" class="social-btn">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="#" class="social-btn">
                                <i class="fab fa-facebook"></i>
                            </a>
                            <a href="#" class="social-btn">
                                <i class="fab fa-linkedin"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</footer>
<script src="https://cdnjs.cloudflare.com/ajax/libs/Swiper/11.0.5/swiper-bundle.min.js"></script>
<script>
    new Swiper('.swiper', {
        slidesPerView: 1,
        spaceBetween: 16,
        loop: true,
        autoplay: { delay: 5000 },
        breakpoints: {
            480: {
                slidesPerView: 1.2,
                spaceBetween: 16
            },
            640: {
                slidesPerView: 1.5,
                spaceBetween: 20
            },
            768: {
                slidesPerView: 2,
                spaceBetween: 24
            },
            1024: {
                slidesPerView: 2.5,
                spaceBetween: 24
            }
        },
        pagination: {
            el: '.swiper-pagination',
            clickable: true,
        }
    });
</script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
<script>
    // Service toggle
    function toggleService(n) {
        const h = document.querySelector(`#service-${n}`).previousElementSibling;
        const c = document.querySelector(`#service-${n}`);
        document.querySelectorAll('.service-header').forEach(x => x !== h && x.classList.remove('active') && x.nextElementSibling.classList.remove('active'));
        h.classList.toggle('active');
        c.classList.toggle('active');
    }

    // Scroll animations with 3 types
    window.onscroll = () => {
        document.querySelectorAll('.scroll-animate').forEach((el, i) => {
            if (el.getBoundingClientRect().top < innerHeight - 100) {
                el.classList.add('show');
                if (el.classList.contains('communicate-heading')) el.classList.add('fade-up');
                if (el.classList.contains('service-item')) el.classList.add('slide-left');
                if (el.classList.contains('swiper')) el.classList.add('zoom');
            }
        });
    };

    // Smooth scrolling
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                const headerHeight = 80;
                const targetPosition = target.offsetTop - headerHeight;
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });



    // Contact form
    document.getElementById('contactForm').addEventListener('submit', function (e) {
        e.preventDefault();
        const button = this.querySelector('button[type="submit"]');
        const originalText = button.textContent;
        button.textContent = 'Sending...';
        button.disabled = true;

        setTimeout(() => {
            alert('🎉 Thank you! Our team will contact you within 24 hours.');
            this.reset();
            button.textContent = originalText;
            button.disabled = false;
        }, 2000);
    });

    // Auto-advance carousel
    const carousel = new bootstrap.Carousel(document.querySelector('#testimonialCarousel'), {
        interval: 5000,
        wrap: true
    });

    // Footer toggle functionality
    const footerToggler = document.querySelector('.footer-toggler');
    const footerNav = document.querySelector('#footerNav');

    if (footerToggler && footerNav) {
        footerToggler.addEventListener('click', function() {
            const icon = this.querySelector('i');
            const isExpanded = this.getAttribute('aria-expanded') === 'true';

            // Toggle icon
            if (isExpanded) {
                icon.classList.remove('fa-times');
                icon.classList.add('fa-bars');
            } else {
                icon.classList.remove('fa-bars');
                icon.classList.add('fa-times');
            }
        });

        // Listen for Bootstrap collapse events
        footerNav.addEventListener('hidden.bs.collapse', function() {
            const icon = footerToggler.querySelector('i');
            icon.classList.remove('fa-times');
            icon.classList.add('fa-bars');
        });

        footerNav.addEventListener('shown.bs.collapse', function() {
            const icon = footerToggler.querySelector('i');
            icon.classList.remove('fa-bars');
            icon.classList.add('fa-times');
        });
    }
</script>
</body>

</html>

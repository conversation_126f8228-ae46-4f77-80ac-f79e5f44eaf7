<?php
// SMTP Email Configuration
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use P<PERSON><PERSON>ailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

// Include PHPMailer (you need to install it via Composer or download manually)
require 'vendor/autoload.php'; // If using Composer
// OR include manually downloaded files:
// require 'PHPMailer/src/Exception.php';
// require 'PHPMailer/src/PHPMailer.php';
// require 'PHPMailer/src/SMTP.php';

// CORS headers for cross-origin requests
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

try {
    // Get form data
    $input = json_decode(file_get_contents('php://input'), true);
    
    $firstName = htmlspecialchars($input['first_name'] ?? '');
    $lastName = htmlspecialchars($input['last_name'] ?? '');
    $email = filter_var($input['email'] ?? '', FILTER_VALIDATE_EMAIL);
    $phone = htmlspecialchars($input['phone_number'] ?? '');
    $message = htmlspecialchars($input['message'] ?? '');
    
    // Validate required fields
    if (empty($firstName) || empty($lastName) || !$email || empty($message)) {
        throw new Exception('All fields are required and email must be valid');
    }
    
    // Create PHPMailer instance
    $mail = new PHPMailer(true);
    
    // SMTP Configuration
    $mail->isSMTP();
    $mail->Host       = 'smtp.gmail.com'; // Change to your SMTP server
    $mail->SMTPAuth   = true;
    $mail->Username   = '<EMAIL>'; // Your email
    $mail->Password   = 'your-app-password'; // Your app password
    $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
    $mail->Port       = 587;
    
    // Email settings
    $mail->setFrom('<EMAIL>', 'VivoLink Contact Form');
    $mail->addAddress('<EMAIL>', 'VivoLink Support');
    $mail->addReplyTo($email, $firstName . ' ' . $lastName);
    
    // Email content
    $mail->isHTML(true);
    $mail->Subject = 'New Contact Form Submission - VivoLink';
    
    $mail->Body = "
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #f40009; color: white; padding: 20px; text-align: center; }
            .content { background: #f9f9f9; padding: 20px; }
            .field { margin-bottom: 15px; }
            .label { font-weight: bold; color: #f40009; }
            .footer { background: #333; color: white; padding: 15px; text-align: center; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h2>New Contact Form Submission</h2>
            </div>
            <div class='content'>
                <div class='field'>
                    <span class='label'>Name:</span> {$firstName} {$lastName}
                </div>
                <div class='field'>
                    <span class='label'>Email:</span> {$email}
                </div>
                <div class='field'>
                    <span class='label'>Phone:</span> {$phone}
                </div>
                <div class='field'>
                    <span class='label'>Message:</span><br>
                    " . nl2br($message) . "
                </div>
            </div>
            <div class='footer'>
                This message was sent from the VivoLink contact form.
            </div>
        </div>
    </body>
    </html>";
    
    // Plain text version
    $mail->AltBody = "
    New Contact Form Submission - VivoLink
    
    Name: {$firstName} {$lastName}
    Email: {$email}
    Phone: {$phone}
    
    Message:
    {$message}
    
    ---
    This message was sent from the VivoLink contact form.
    ";
    
    // Send email
    $mail->send();
    
    // Success response
    echo json_encode([
        'success' => true,
        'message' => 'Thank you! Your message has been sent successfully. Our team will contact you within 24 hours.'
    ]);
    
} catch (Exception $e) {
    // Error response
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Sorry, there was an error sending your message. Please try again.',
        'error' => $e->getMessage()
    ]);
}
?>
